'use client';

import React from 'react';
import { cn } from '@/utils/cn';
import { sanitizeHtml } from '@/utils/sanitizeHtml';
import { Question } from './QuestionListingView';

export interface DragOverlayProps {
  question: Question;
  questionIndex: number;
  isHtmlContent?: boolean;
}

/**
 * DragOverlay component for visual feedback during drag operations
 * Shows a simplified version of the question card with enhanced visual effects
 */
export const DragOverlay: React.FC<DragOverlayProps> = ({
  question,
  questionIndex,
  isHtmlContent = false,
}) => {
  return (
    <div
      className={cn(
        // Base card styling
        'p-4 md:p-6 bg-white border border-gray-200 rounded-xl',
        
        // Drag overlay specific effects
        'opacity-90 shadow-2xl',
        'transform rotate-2 scale-105',
        'ring-2 ring-blue-500 ring-opacity-50',
        
        // Ensure it's above everything
        'z-50',
        
        // Smooth transitions
        'transition-all duration-200 ease-out'
      )}
    >
      {/* Question Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 md:mb-5 pb-2 md:pb-3 border-b border-gray-200">
        <span className="text-lg md:text-xl font-semibold text-gray-700 mb-2 sm:mb-0">
          Question {questionIndex + 1}
        </span>
        <div className="flex items-center gap-2 md:gap-3">
          {question.subject && (
            <span className="text-xs px-2 md:px-3 py-1 bg-blue-100 text-blue-700 rounded-full font-medium">
              {question.subject}
            </span>
          )}
          <span className="text-xs px-2 md:px-3 py-1 bg-gray-100 text-gray-600 rounded-full font-medium capitalize">
            {question.type.replace('_', ' ')}
          </span>
        </div>
      </div>

      {/* Question Content - Truncated for overlay */}
      <div className="mb-4">
        {isHtmlContent ? (
          <div
            className="text-gray-800 text-sm md:text-base leading-relaxed line-clamp-3"
            dangerouslySetInnerHTML={{
              __html: sanitizeHtml(question.content),
            }}
          />
        ) : (
          <div className="text-gray-800 text-sm md:text-base leading-relaxed">
            {question.content.length > 150
              ? `${question.content.substring(0, 150)}...`
              : question.content}
          </div>
        )}
      </div>

      {/* Options Preview - Show first few options */}
      {(question.type === 'multiple_choice' || question.type === 'single_choice') &&
        question?.options?.length > 0 && (
          <div className="space-y-1">
            {question.options.slice(0, 2).map((option, optionIndex) => {
              const isAnswer = question.answer?.includes(option);
              return (
                <div
                  key={optionIndex}
                  className={cn(
                    'flex items-center gap-2 p-2 rounded-md text-xs md:text-sm',
                    isAnswer
                      ? 'bg-green-50 border border-green-200'
                      : 'bg-gray-50 border border-gray-200'
                  )}
                >
                  <div
                    className={cn(
                      'w-3 h-3 rounded border-2 flex-shrink-0',
                      isAnswer
                        ? 'bg-green-500 border-green-500'
                        : 'border-gray-300'
                    )}
                  />
                  <span className={cn(
                    'truncate',
                    isAnswer ? 'text-green-800 font-medium' : 'text-gray-700'
                  )}>
                    {option}
                  </span>
                </div>
              );
            })}
            {question.options.length > 2 && (
              <div className="text-xs text-gray-500 italic">
                +{question.options.length - 2} more options...
              </div>
            )}
          </div>
        )}

      {/* Drag indicator */}
      <div className="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center shadow-lg">
        <div className="w-2 h-2 bg-white rounded-full"></div>
      </div>
    </div>
  );
};

export default DragOverlay;
