'use client';

import { cn } from '@/utils/cn';
import { sanitizeHtml } from '@/utils/sanitizeHtml';
import React, { useState, useCallback, useMemo } from 'react';
import Icon from '@/components/atoms/Icon';
import { CreativeWritingRenderer } from '@/components/molecules/QuestionRenderer/CreativeWritingRenderer';
import { FillBlankContentProcessor } from '@/components/molecules/QuestionRenderer/FillBlankContentProcessor';

// DnD Kit imports
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay as DndDragOverlay,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

// Local components
import { DragHandle } from './DragHandle';
import { ReorderControls } from './ReorderControls';
import { DragOverlay } from './DragOverlay';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';

// Server action
import { reorderWorksheetQuestionsAction } from '@/actions/worksheet.action';

// Define the type for a single question
export type Question = {
  questionId?: string; // Required for server reordering API
  type:
    | 'multiple_choice'
    | 'single_choice'
    | 'fill_blank'
    | 'creative_writing'
    | string;
  content: string;
  image?: string | null;
  svgCode?: string;
  imagePrompt?: string | null;
  options: string[];
  answer: string[];
  explain: string;
  prompt?: string; // For creative writing prompts
  subject?: string; // Subject of the question from API response
};

// Define the props type for the component
export type QuestionListingViewProps = {
  questions?: Question[];
  containerClass?: string;
  isHtmlContent?: boolean;
  worksheetInfo?: {
    topic?: string;
    subject?: string; // Added subject
    grade?: string;
    language?: string;
    level?: string;
    totalQuestions?: number;
  };
  // New props for drag-and-drop functionality
  worksheetId?: string; // Required for server updates
  onOrderChange?: (newOrder: Question[]) => void; // Callback for order changes
  readOnly?: boolean; // Disable reordering when needed
};

const QuestionListingView: React.FC<QuestionListingViewProps> = ({
  questions = [],
  containerClass = '',
  isHtmlContent = false,
  worksheetInfo,
  worksheetId,
  onOrderChange,
  readOnly = false,
}) => {
  // Debug props on render
  console.log('🎯 QuestionListingView render:', {
    worksheetId: worksheetId,
    worksheetIdType: typeof worksheetId,
    questionsLength: questions.length,
    readOnly,
    hasOnOrderChange: !!onOrderChange
  });

  // State for drag-and-drop functionality
  const [localQuestions, setLocalQuestions] = useState<Question[]>(questions);
  const [originalOrder, setOriginalOrder] = useState<Question[]>(questions);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [feedback, setFeedback] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  const [announcement, setAnnouncement] = useState<string>('');

  // Update local state when questions prop changes
  React.useEffect(() => {
    console.log('📥 Questions prop changed:', questions.length, 'questions');
    setLocalQuestions(questions);
    setOriginalOrder(questions);
  }, [questions]);

  // Check if order has changed
  const hasChanges = useMemo(() => {
    if (localQuestions.length !== originalOrder.length) return true;

    // Compare order by checking if questions at each position are the same object reference
    // This works because arrayMove moves the same objects to different positions
    const changed = localQuestions.some((q, index) => q !== originalOrder[index]);
    console.log('🔍 hasChanges check:', { changed, localLength: localQuestions.length, originalLength: originalOrder.length });

    return changed;
  }, [localQuestions, originalOrder]);

  // DnD Kit sensors with mobile-optimized settings
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Require 8px movement before drag starts
        tolerance: 5, // Allow 5px tolerance for touch
        delay: 100, // 100ms delay to distinguish from scrolling
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Drag handlers
  const handleDragStart = useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id as string);
    const questionIndex = parseInt((event.active.id as string).replace('question-', ''));
    setAnnouncement(`Picked up question ${questionIndex + 1}. Use arrow keys to move, or drag to reorder.`);
  }, []);

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);

    if (over && active.id !== over.id) {
      setLocalQuestions((items) => {
        const oldIndex = items.findIndex((_, index) => `question-${index}` === active.id);
        const newIndex = items.findIndex((_, index) => `question-${index}` === over.id);

        console.log('🔄 Drag end - moving question from', oldIndex, 'to', newIndex);

        const newOrder = arrayMove(items, oldIndex, newIndex);
        onOrderChange?.(newOrder);

        // Announce the move
        setAnnouncement(`Question ${oldIndex + 1} moved to position ${newIndex + 1}.`);

        return newOrder;
      });
    } else {
      setAnnouncement('Question dropped in original position.');
    }
  }, [onOrderChange]);

  // Manual reorder functions
  const moveQuestionUp = useCallback((index: number) => {
    if (index > 0) {
      setLocalQuestions((items) => {
        const newOrder = arrayMove(items, index, index - 1);
        onOrderChange?.(newOrder);
        setAnnouncement(`Question ${index + 1} moved up to position ${index}.`);
        return newOrder;
      });
    }
  }, [onOrderChange]);

  const moveQuestionDown = useCallback((index: number) => {
    if (index < localQuestions.length - 1) {
      setLocalQuestions((items) => {
        const newOrder = arrayMove(items, index, index + 1);
        onOrderChange?.(newOrder);
        setAnnouncement(`Question ${index + 1} moved down to position ${index + 2}.`);
        return newOrder;
      });
    }
  }, [localQuestions.length, onOrderChange]);

  // Server update function
  const updateServerOrder = useCallback(async () => {
    console.log('🔄 updateServerOrder called', {
      worksheetId: worksheetId, // Show actual value, not just boolean
      worksheetIdType: typeof worksheetId,
      hasChanges,
      questionsLength: localQuestions.length,
      originalLength: originalOrder.length
    });

    if (!worksheetId || !hasChanges) {
      console.log('⚠️ Early return:', {
        worksheetId: worksheetId,
        worksheetIdBoolean: !!worksheetId,
        hasChanges,
        condition1: !worksheetId,
        condition2: !hasChanges
      });

      // If no worksheetId, just show a message that changes are local only
      if (!worksheetId && hasChanges) {
        console.log('📝 Showing local-only message');
        setFeedback({
          type: 'success',
          message: 'Question order updated locally. Provide worksheetId prop to save to server.'
        });
        setTimeout(() => setFeedback(null), 3000);
      } else if (worksheetId && !hasChanges) {
        console.log('📝 No changes detected, not updating');
      }
      return;
    }

    // Check if we have questionIds for server update
    const questionsWithIds = localQuestions.filter(q => q.questionId);
    console.log('📋 Questions with IDs:', questionsWithIds.length, 'out of', localQuestions.length);

    if (questionsWithIds.length === 0) {
      console.log('❌ No questions with questionId found');
      setFeedback({
        type: 'error',
        message: 'Cannot save to server: questions need questionId field for server updates.'
      });
      setTimeout(() => setFeedback(null), 5000);
      return;
    }

    console.log('🚀 Starting server update...');
    setIsUpdating(true);
    setFeedback(null);

    try {
      const reorderData = {
        reorders: questionsWithIds.map((question) => ({
          questionId: question.questionId!,
          newPosition: localQuestions.findIndex(q => q === question) + 1,
        })),
      };

      console.log('📤 Sending reorder data:', reorderData);
      const result = await reorderWorksheetQuestionsAction(worksheetId, reorderData);
      console.log('📥 Server response:', result);

      if (result.status === 'success') {
        setOriginalOrder([...localQuestions]);
        setFeedback({ type: 'success', message: 'Question order updated successfully!' });
        setTimeout(() => setFeedback(null), 3000);
      } else {
        throw new Error(result.message || 'Failed to update question order');
      }
    } catch (error) {
      console.error('Error updating question order:', error);
      // Rollback to original order
      setLocalQuestions([...originalOrder]);
      onOrderChange?.(originalOrder);
      setFeedback({
        type: 'error',
        message: error instanceof Error ? error.message : 'Failed to update question order'
      });
      setTimeout(() => setFeedback(null), 5000);
    } finally {
      setIsUpdating(false);
    }
  }, [worksheetId, hasChanges, localQuestions, originalOrder, onOrderChange]);

  // Get the active question for drag overlay
  const activeQuestion = useMemo(() => {
    if (!activeId) return null;
    const index = parseInt(activeId.replace('question-', ''));
    return localQuestions[index] || null;
  }, [activeId, localQuestions]);

  const activeQuestionIndex = useMemo(() => {
    if (!activeId) return -1;
    return parseInt(activeId.replace('question-', ''));
  }, [activeId]);

  // Enable drag and drop only if not read-only and we have a worksheetId for server updates
  // For local reordering, we don't need questionId - we can use array indices
  const enableDragAndDrop = !readOnly && localQuestions.length > 0;

  // Question Content Component (extracted for reuse)
  const QuestionContent: React.FC<{ question: Question; index: number; isHtmlContent: boolean }> = ({
    question,
    index,
    isHtmlContent
  }) => (
    <>
      {/* Question Content */}
      <div className="bg-gray-50 p-3 md:p-5 rounded-lg mb-3 md:mb-5">
        {question.type === 'fill_blank' ? (
          <FillBlankContentProcessor
            content={question.content}
            answers={question.answer}
            isHtmlContent={isHtmlContent}
          />
        ) : isHtmlContent ? (
          <div
            className="text-sm md:text-base text-gray-800 leading-relaxed question-content-html"
            dangerouslySetInnerHTML={{ __html: question.content }}
          />
        ) : (
          <p className="text-sm md:text-base text-gray-800 leading-relaxed">{question.content}</p>
        )}
      </div>

      {/* SVG Illustration or Image (if available) */}
      {question?.image && (
        <div
          className="my-4 w-full max-w-lg"
          dangerouslySetInnerHTML={{ __html: question.image }}
        />
      )}

      {/* Question Type Specific Rendering */}
      {/* Multiple Choice & Single Choice Options */}
      {(question.type === 'multiple_choice' || question.type === 'single_choice') &&
        question?.options?.length > 0 && (
          <div className="space-y-1 mt-3 md:mt-5">
            {question?.options?.map((option, optionIndex) => {
              const isAnswer = question.answer?.includes(option);
              const isSingleChoice = question.type === 'single_choice';
              const inputType = isSingleChoice ? 'radio' : 'checkbox';

              return (
                <label
                  key={optionIndex}
                  className="flex items-start space-x-2 md:space-x-3 cursor-pointer py-1.5 md:py-3 border border-gray-100 hover:bg-gray-50 rounded-md px-2 md:px-3 mb-1.5 md:mb-2"
                >
                  <input
                    type={inputType}
                    name={`question-${index}`}
                    value={option}
                    defaultChecked={isAnswer}
                    disabled={!isAnswer}
                    className={cn(
                      inputType === 'radio'
                        ? 'radio radio-primary radio-sm md:radio-md'
                        : 'checkbox checkbox-primary checkbox-sm md:checkbox-md',
                      'mt-0.5 md:mt-1 border-2',
                      isAnswer ? 'border-primary' : 'border-gray-300'
                    )}
                  />
                  {isHtmlContent ? (
                    <span
                      className={cn(
                        'flex-1 pt-0 md:pt-0.5 text-sm md:text-base leading-snug md:leading-relaxed',
                        isAnswer && 'font-medium text-primary'
                      )}
                      dangerouslySetInnerHTML={{ __html: option }}
                    />
                  ) : (
                    <span
                      className={cn(
                        'flex-1 pt-0 md:pt-0.5 text-sm md:text-base leading-snug md:leading-relaxed',
                        isAnswer && 'font-medium text-primary'
                      )}
                    >
                      {option}
                    </span>
                  )}
                </label>
              );
            })}
          </div>
        )}

      {/* Creative Writing */}
      {question.type === 'creative_writing' && (
        <CreativeWritingRenderer
          answer={question.answer}
          prompt={question.prompt}
          isHtmlContent={isHtmlContent}
          minWords={50}
          maxWords={300}
        />
      )}

      {/* Explanation Accordion */}
      {question?.explain && (
        <ExplanationAccordion
          explanation={question.explain}
          isHtmlContent={isHtmlContent}
        />
      )}
    </>
  );

  // Sortable Question Card Component
  const SortableQuestionCard: React.FC<{ question: Question; index: number }> = ({ question, index }) => {
    const [isHovered, setIsHovered] = useState(false);
    const [isMobile, setIsMobile] = useState(false);

    // Check if mobile on mount and resize
    React.useEffect(() => {
      const checkMobile = () => setIsMobile(window.innerWidth < 768);
      checkMobile();
      window.addEventListener('resize', checkMobile);
      return () => window.removeEventListener('resize', checkMobile);
    }, []);

    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging,
    } = useSortable({
      id: `question-${index}`,
      disabled: !enableDragAndDrop,
    });

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
    };

    return (
      <div
        ref={setNodeRef}
        style={style}
        className={cn(
          'mb-4 md:mb-8 bg-white border border-gray-200 rounded-xl shadow-lg',
          'transition-all duration-200',
          isDragging && 'opacity-50 scale-95',
          enableDragAndDrop && 'group'
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        role="article"
        aria-label={`Question ${index + 1}: ${question.type.replace('_', ' ')}`}
        aria-describedby={enableDragAndDrop ? `question-${index}-instructions` : undefined}
      >
        <div className="relative p-4 md:p-6">
          {/* Drag Handle and Reorder Controls */}
          {enableDragAndDrop && (
            <div className="absolute left-1 md:left-2 top-3 md:top-4 flex items-start gap-1 md:gap-2">
              <DragHandle
                listeners={listeners}
                attributes={attributes}
                disabled={!enableDragAndDrop}
              />
              <ReorderControls
                isVisible={isHovered || isMobile} // Always visible on mobile
                canMoveUp={index > 0}
                canMoveDown={index < localQuestions.length - 1}
                hasChanges={hasChanges}
                isUpdating={isUpdating}
                onMoveUp={() => moveQuestionUp(index)}
                onMoveDown={() => moveQuestionDown(index)}
                onUpdateOrder={updateServerOrder}
              />
            </div>
          )}

          {/* Question Content */}
          <div className={cn(enableDragAndDrop && 'ml-10 md:ml-12')}>
            {/* Question Index, Type and Subject */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 md:mb-5 pb-2 md:pb-3 border-b border-gray-200">
              <span className="text-lg md:text-xl font-semibold text-gray-700 mb-2 sm:mb-0">
                Question {index + 1}
              </span>
              <div className="flex items-center gap-2 md:gap-3">
                {question.subject && (
                  <span className="text-xs px-2 md:px-3 py-1 bg-blue-100 text-blue-700 rounded-full font-medium">
                    {question.subject}
                  </span>
                )}
                <span className="text-xs px-2 md:px-3 py-1 bg-gray-100 text-gray-600 rounded-full font-medium capitalize">
                  {question.type.replace('_', ' ')}
                </span>
              </div>
            </div>

            {/* Rest of the question content */}
            <QuestionContent question={question} index={index} isHtmlContent={isHtmlContent} />
          </div>

          {/* Hidden instructions for screen readers */}
          {enableDragAndDrop && (
            <div id={`question-${index}-instructions`} className="sr-only">
              This question can be reordered. Use the drag handle or arrow buttons to change position.
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={cn('space-y-8', containerClass)}>
      {/* Screen Reader Live Region for Announcements */}
      <div
        aria-live="polite"
        aria-atomic="true"
        className="sr-only"
        role="status"
      >
        {announcement}
      </div>

      {/* Feedback Messages */}
      {feedback && (
        <AlertMessage type={feedback.type} message={feedback.message} />
      )}

      {/* Worksheet Information - Sticky and Mobile-Optimized */}
      {worksheetInfo && (
        <div className="sticky top-0 z-50 bg-white border-b border-gray-200 shadow-sm mb-4">
          {/* Mobile Layout - Minimal and Compact */}
          <div className="block md:hidden px-3 py-2">
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center gap-2">
                {worksheetInfo.subject && (
                  <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full font-medium">
                    {worksheetInfo.subject}
                  </span>
                )}
                {worksheetInfo.grade && (
                  <span className="px-2 py-1 bg-green-100 text-green-700 rounded-full font-medium">
                    {worksheetInfo.grade}
                  </span>
                )}
              </div>
              {worksheetInfo.totalQuestions && (
                <span className="text-gray-600 font-medium">
                  {worksheetInfo.totalQuestions} Q
                </span>
              )}
            </div>
            {/* Secondary info on mobile - minimal */}
            {(worksheetInfo.topic || worksheetInfo.language || worksheetInfo.level) && (
              <div className="flex items-center gap-2 mt-1 text-xs text-gray-500 truncate">
                {worksheetInfo.topic && <span className="truncate">{worksheetInfo.topic}</span>}
                {worksheetInfo.language && <span>• {worksheetInfo.language}</span>}
                {worksheetInfo.level && <span>• {worksheetInfo.level}</span>}
              </div>
            )}
          </div>

          {/* Desktop Layout - Full Details */}
          <div className="hidden md:block p-3">
            <div className="flex flex-wrap gap-x-6 gap-y-1 text-sm">
              {worksheetInfo.subject && (
                <div className="flex items-center">
                  <span className="font-medium text-gray-600">Subject:</span>
                  <span className="ml-2 text-primary">{worksheetInfo.subject}</span>
                </div>
              )}
              {worksheetInfo.topic && (
                <div className="flex items-center">
                  <span className="font-medium text-gray-600">Topic:</span>
                  <span className="ml-2 text-primary">{worksheetInfo.topic}</span>
                </div>
              )}
              {worksheetInfo.grade && (
                <div className="flex items-center">
                  <span className="font-medium text-gray-600">Grade:</span>
                  <span className="ml-2 text-primary">{worksheetInfo.grade}</span>
                </div>
              )}
              {worksheetInfo.level && (
                <div className="flex items-center">
                  <span className="font-medium text-gray-600">Level:</span>
                  <span className="ml-2 text-primary">{worksheetInfo.level}</span>
                </div>
              )}
              {worksheetInfo.language && (
                <div className="flex items-center">
                  <span className="font-medium text-gray-600">Language:</span>
                  <span className="ml-2 text-primary">
                    {worksheetInfo.language}
                  </span>
                </div>
              )}
              {worksheetInfo.totalQuestions && (
                <div className="flex items-center">
                  <span className="font-medium text-gray-600">
                    Total Questions:
                  </span>
                  <span className="ml-2 text-primary">
                    {worksheetInfo.totalQuestions}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Questions List with Drag and Drop */}
      {enableDragAndDrop ? (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={localQuestions.map((_, index) => `question-${index}`)}
            strategy={verticalListSortingStrategy}
          >
            {localQuestions.map((question, index) => (
              <SortableQuestionCard
                key={question.questionId || `question-${index}`}
                question={question}
                index={index}
              />
            ))}
          </SortableContext>

          <DndDragOverlay>
            {activeQuestion && activeQuestionIndex >= 0 ? (
              <DragOverlay
                question={activeQuestion}
                questionIndex={activeQuestionIndex}
                isHtmlContent={isHtmlContent}
              />
            ) : null}
          </DndDragOverlay>
        </DndContext>
      ) : (
        /* Fallback for read-only or when drag-and-drop is disabled */
        localQuestions.map((question, index) => (
          <div
            key={question.questionId || `question-${index}`}
            className="mb-4 md:mb-8 p-4 md:p-6 bg-white border border-gray-200 rounded-xl shadow-lg"
          >
            {/* Question Index, Type and Subject */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 md:mb-5 pb-2 md:pb-3 border-b border-gray-200">
              <span className="text-lg md:text-xl font-semibold text-gray-700 mb-2 sm:mb-0">
                Question {index + 1}
              </span>
              <div className="flex items-center gap-2 md:gap-3">
                {question.subject && (
                  <span className="text-xs px-2 md:px-3 py-1 bg-blue-100 text-blue-700 rounded-full font-medium">
                    {question.subject}
                  </span>
                )}
                <span className="text-xs px-2 md:px-3 py-1 bg-gray-100 text-gray-600 rounded-full font-medium capitalize">
                  {question.type.replace('_', ' ')}
                </span>
              </div>
            </div>

            <QuestionContent question={question} index={index} isHtmlContent={isHtmlContent} />
          </div>
        ))
      )}
    </div>
  );
};

// Explanation Accordion Component
type ExplanationAccordionProps = {
  explanation: string;
  isHtmlContent?: boolean;
};

const ExplanationAccordion: React.FC<ExplanationAccordionProps> = ({
  explanation,
  isHtmlContent = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="mt-3 md:mt-5 border border-gray-200 rounded-md overflow-hidden shadow-sm">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between py-2 md:py-2.5 px-3 md:px-4 bg-gray-50 focus:outline-none transition-colors hover:bg-gray-100"
      >
        <div className="flex items-center gap-2">
          <Icon
            variant="chevron-down"
            size={3.5}
            className={cn(
              'transition-transform duration-200 text-primary',
              isOpen && 'rotate-180'
            )}
          />
          <span className="font-medium text-primary text-xs md:text-sm">
            View Explanation
          </span>
        </div>
      </button>

      <div
        className={cn(
          'transition-all duration-300 ease-in-out overflow-hidden',
          isOpen ? 'max-h-[1000px] opacity-100' : 'max-h-0 opacity-0'
        )}
      >
        <div className="p-3 md:p-4 bg-white border-t border-gray-200">
          {isHtmlContent ? (
            <div
              className="text-gray-700 text-xs md:text-sm leading-relaxed max-w-none"
              dangerouslySetInnerHTML={{
                __html: sanitizeHtml(explanation),
              }}
            />
          ) : (
            <div className="text-gray-700 text-xs md:text-sm leading-relaxed">
              {explanation.split('\n').map((paragraph, i) =>
                paragraph.trim() ? (
                  <p key={i} className="mb-2 md:mb-3">
                    {paragraph}
                  </p>
                ) : null
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default QuestionListingView;
