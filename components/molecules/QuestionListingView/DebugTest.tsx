'use client';

import React, { useState } from 'react';
import QuestionListingView, { Question } from './QuestionListingView';

// Minimal test data
const testQuestions: Question[] = [
  {
    questionId: 'test-q1',
    type: 'multiple_choice',
    content: 'Test Question 1?',
    options: ['A', 'B', 'C', 'D'],
    answer: ['A'],
    explain: 'Test explanation',
    subject: 'Test'
  },
  {
    questionId: 'test-q2',
    type: 'single_choice',
    content: 'Test Question 2?',
    options: ['X', 'Y', 'Z'],
    answer: ['Y'],
    explain: 'Test explanation 2',
    subject: 'Test'
  }
];

/**
 * Minimal debug test for QuestionListingView
 */
export const DebugTest: React.FC = () => {
  const [questions, setQuestions] = useState<Question[]>(testQuestions);

  const handleOrderChange = (newOrder: Question[]) => {
    console.log('🔄 Order changed in parent:', newOrder.map(q => q.questionId));
    setQuestions(newOrder);
  };

  console.log('🧪 DebugTest render - passing worksheetId:', 'debug-worksheet-123');

  return (
    <div className="max-w-2xl mx-auto p-4">
      <h1 className="text-xl font-bold mb-4">Debug Test</h1>
      
      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm">
          <strong>Test:</strong> Drag questions and click "Update Order"
        </p>
        <p className="text-sm">
          <strong>Expected:</strong> Should call server action (check console)
        </p>
        <p className="text-sm">
          <strong>WorksheetId:</strong> "debug-worksheet-123"
        </p>
      </div>

      <QuestionListingView
        questions={questions}
        worksheetId="debug-worksheet-123"
        onOrderChange={handleOrderChange}
        readOnly={false}
        isHtmlContent={false}
      />
    </div>
  );
};

export default DebugTest;
