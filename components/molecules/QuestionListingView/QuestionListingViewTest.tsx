'use client';

import React, { useState } from 'react';
import QuestionListingView, { Question } from './QuestionListingView';

// Sample test data
const sampleQuestions: Question[] = [
  {
    questionId: 'q1',
    type: 'multiple_choice',
    content: 'What is the capital of France?',
    options: ['London', 'Berlin', 'Paris', 'Madrid'],
    answer: ['Paris'],
    explain: 'Paris is the capital and largest city of France.',
    subject: 'Geography'
  },
  {
    questionId: 'q2',
    type: 'single_choice',
    content: 'Which planet is closest to the Sun?',
    options: ['Venus', 'Mercury', 'Earth', 'Mars'],
    answer: ['Mercury'],
    explain: 'Mercury is the smallest planet in our solar system and the closest to the Sun.',
    subject: 'Science'
  },
  {
    questionId: 'q3',
    type: 'fill_blank',
    content: 'The chemical symbol for water is ___.',
    options: [],
    answer: ['H2O'],
    explain: 'Water is composed of two hydrogen atoms and one oxygen atom, hence H2O.',
    subject: 'Chemistry'
  },
  {
    questionId: 'q4',
    type: 'creative_writing',
    content: 'Write a short story about a magical forest.',
    options: [],
    answer: ['<PERSON>ple creative writing answer...'],
    explain: 'Creative writing helps develop imagination and language skills.',
    prompt: 'Describe the magical creatures and enchanted elements you might find.',
    subject: 'English'
  }
];

/**
 * Test component for QuestionListingView with drag-and-drop functionality
 * This component demonstrates how to use the enhanced QuestionListingView
 */
export const QuestionListingViewTest: React.FC = () => {
  const [questions, setQuestions] = useState<Question[]>(sampleQuestions);
  const [readOnly, setReadOnly] = useState(false);

  const handleOrderChange = (newOrder: Question[]) => {
    console.log('Order changed:', newOrder.map(q => q.questionId));
    setQuestions(newOrder);
  };

  const worksheetInfo = {
    topic: 'General Knowledge Quiz',
    subject: 'Mixed Subjects',
    grade: 'Grade 8',
    language: 'English',
    level: 'Intermediate',
    totalQuestions: questions.length
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">Question Listing View Test</h1>
        
        {/* Controls */}
        <div className="flex items-center gap-4 mb-4 p-4 bg-gray-100 rounded-lg">
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={readOnly}
              onChange={(e) => setReadOnly(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm font-medium">Read Only Mode</span>
          </label>
          
          <div className="text-sm text-gray-600">
            Questions: {questions.length} | 
            Drag & Drop: {readOnly ? 'Disabled' : 'Enabled'}
          </div>
        </div>

        {/* Instructions */}
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-2">Test Instructions:</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Hover over question cards to see reorder controls</li>
            <li>• Use the drag handle (6-dot icon) to drag questions</li>
            <li>• Use arrow buttons for manual reordering</li>
            <li>• Click &quot;Update Order&quot; to save changes (local only in this test)</li>
            <li>• Toggle &quot;Read Only Mode&quot; to disable reordering</li>
            <li>• Test on mobile devices for touch interactions</li>
          </ul>
        </div>
      </div>

      {/* Question Listing View */}
      <QuestionListingView
        questions={questions}
        worksheetInfo={worksheetInfo}
        onOrderChange={handleOrderChange}
        readOnly={readOnly}
        isHtmlContent={false}
        // worksheetId="test-worksheet" // Uncomment to test server integration
      />
    </div>
  );
};

export default QuestionListingViewTest;
